import { NextRequest, NextResponse } from "next/server"
import { withAuth } from "@/lib/middleware/auth-middleware"
import { AuditService } from "@/lib/services/audit-service"
import { updateAuditSchema } from "@/lib/validations/audit"
import { hasPermission, UserRole } from "@/lib/validations/user"
import { z } from "zod"

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * GET /api/audits/[id] - Obtenir un audit par ID
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'read')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      const audit = await AuditService.getAuditById(params.id)

      if (!audit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      // Vérifier l'accès selon le rôle
      const canAccess = 
        user.role === UserRole.SUPER_ADMIN ||
        audit.organizationId === user.organizationId ||
        audit.auditors.some(a => a.user.id === user.id) ||
        audit.creatorId === user.id

      if (!canAccess) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé à cet audit" },
          { status: 403 }
        )
      }

      return NextResponse.json({
        success: true,
        data: audit
      })

    } catch (error) {
      console.error("Erreur lors de la récupération de l'audit:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * PUT /api/audits/[id] - Mettre à jour un audit
 */
export async function PUT(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'update')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe et que l'utilisateur peut le modifier
      const existingAudit = await AuditService.getAuditById(params.id)
      
      if (!existingAudit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      const canUpdate = 
        user.role === UserRole.SUPER_ADMIN ||
        user.role === UserRole.MANAGER ||
        (existingAudit.organizationId === user.organizationId && 
         (existingAudit.creatorId === user.id || 
          existingAudit.auditors.some(a => a.user.id === user.id && a.role === 'LEAD_AUDITOR')))

      if (!canUpdate) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé pour modifier cet audit" },
          { status: 403 }
        )
      }

      const body = await req.json()
      const validatedData = updateAuditSchema.parse(body)

      // Si l'utilisateur n'est pas admin, empêcher la modification de l'organisation
      if (user.role !== UserRole.SUPER_ADMIN && validatedData.organizationId) {
        delete validatedData.organizationId
      }

      const updatedAudit = await AuditService.updateAudit(params.id, validatedData)

      return NextResponse.json({
        success: true,
        data: updatedAudit
      })

    } catch (error) {
      console.error("Erreur lors de la mise à jour de l'audit:", error)
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { 
            success: false, 
            error: "Données invalides",
            details: error.errors
          },
          { status: 400 }
        )
      }

      if (error instanceof Error) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        )
      }

      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}

/**
 * DELETE /api/audits/[id] - Supprimer un audit
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  return withAuth(request, async (req, user) => {
    try {
      // Vérifier les permissions
      if (!hasPermission(user.role as UserRole, 'audits', 'delete')) {
        return NextResponse.json(
          { success: false, error: "Permissions insuffisantes" },
          { status: 403 }
        )
      }

      // Vérifier que l'audit existe et que l'utilisateur peut le supprimer
      const existingAudit = await AuditService.getAuditById(params.id)
      
      if (!existingAudit) {
        return NextResponse.json(
          { success: false, error: "Audit non trouvé" },
          { status: 404 }
        )
      }

      const canDelete = 
        user.role === UserRole.SUPER_ADMIN ||
        (user.role === UserRole.MANAGER && existingAudit.organizationId === user.organizationId) ||
        existingAudit.creatorId === user.id

      if (!canDelete) {
        return NextResponse.json(
          { success: false, error: "Accès non autorisé pour supprimer cet audit" },
          { status: 403 }
        )
      }

      // Empêcher la suppression d'audits en cours ou terminés
      if (existingAudit.status === 'IN_PROGRESS' || existingAudit.status === 'COMPLETED') {
        return NextResponse.json(
          { success: false, error: "Impossible de supprimer un audit en cours ou terminé" },
          { status: 400 }
        )
      }

      await AuditService.deleteAudit(params.id)

      return NextResponse.json({
        success: true,
        message: "Audit supprimé avec succès"
      })

    } catch (error) {
      console.error("Erreur lors de la suppression de l'audit:", error)
      return NextResponse.json(
        { success: false, error: "Erreur interne du serveur" },
        { status: 500 }
      )
    }
  })
}
