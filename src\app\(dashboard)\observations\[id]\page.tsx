"use client"

import { useEffect, useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  ObservationSeverityBadge, 
  ObservationStatusBadge 
} from "@/components/features/observations"
import { useObservationActions } from "@/hooks/use-observation-actions"
import { ObservationWithRelations } from "@/lib/validations/observation"
import { 
  ArrowLeft, 
  Edit, 
  CheckCircle, 
  XCircle, 
  RotateCcw, 
  Calendar, 
  FileText, 
  AlertTriangle,
  Building
} from "lucide-react"
import { useRouter } from "next/navigation"
import { format } from "date-fns"
import { fr } from "date-fns/locale"

interface ObservationDetailPageProps {
  params: {
    id: string
  }
}

export default function ObservationDetailPage({ params }: ObservationDetailPageProps) {
  const router = useRouter()
  const [observation, setObservation] = useState<ObservationWithRelations | null>(null)
  
  const {
    loading,
    error,
    getObservation,
    resolveObservation,
    closeObservation,
    reopenObservation,
    clearError
  } = useObservationActions()

  useEffect(() => {
    const fetchObservation = async () => {
      const observationData = await getObservation(params.id)
      if (observationData) {
        setObservation(observationData)
      }
    }

    fetchObservation()
  }, [params.id, getObservation])

  const formatDate = (date: Date) => {
    return format(date, "dd MMMM yyyy", { locale: fr })
  }

  const formatDateTime = (date: Date) => {
    return format(date, "dd/MM/yyyy à HH:mm", { locale: fr })
  }

  const handleResolve = async () => {
    if (!observation) return
    
    if (confirm(`Marquer l'observation "${observation.title}" comme résolue ?`)) {
      const resolved = await resolveObservation(observation.id)
      if (resolved) {
        setObservation(resolved)
      }
    }
  }

  const handleClose = async () => {
    if (!observation) return
    
    if (confirm(`Fermer définitivement l'observation "${observation.title}" ?`)) {
      const closed = await closeObservation(observation.id)
      if (closed) {
        setObservation(closed)
      }
    }
  }

  const handleReopen = async () => {
    if (!observation) return
    
    if (confirm(`Rouvrir l'observation "${observation.title}" ?`)) {
      const reopened = await reopenObservation(observation.id)
      if (reopened) {
        setObservation(reopened)
      }
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 rounded animate-pulse" />
        <div className="h-64 bg-gray-200 rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="ghost"
              size="sm"
              onClick={clearError}
              className="ml-2"
            >
              Fermer
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!observation) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Observation non trouvée
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const canResolve = observation.status === "OPEN" || observation.status === "IN_PROGRESS"
  const canClose = observation.status === "RESOLVED"
  const canReopen = observation.status === "RESOLVED" || observation.status === "CLOSED"
  const canEdit = observation.status !== "CLOSED" && observation.status !== "REJECTED"

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Retour
          </Button>
          
          <div>
            <h1 className="text-3xl font-bold magneto-title">{observation.title}</h1>
            <p className="text-gray-600">
              Audit: {observation.audit.title}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <ObservationSeverityBadge severity={observation.severity} />
          <ObservationStatusBadge status={observation.status} />
          
          {canEdit && (
            <Button
              variant="outline"
              onClick={() => router.push(`/observations/${observation.id}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Modifier
            </Button>
          )}

          {canResolve && (
            <Button
              variant="outline"
              onClick={handleResolve}
              className="text-green-600 hover:text-green-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Résoudre
            </Button>
          )}

          {canClose && (
            <Button
              variant="outline"
              onClick={handleClose}
              className="text-blue-600 hover:text-blue-700"
            >
              <XCircle className="h-4 w-4 mr-2" />
              Fermer
            </Button>
          )}

          {canReopen && observation.status !== "OPEN" && (
            <Button
              variant="outline"
              onClick={handleReopen}
              className="text-orange-600 hover:text-orange-700"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Rouvrir
            </Button>
          )}
        </div>
      </div>

      {/* Informations principales */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Dates
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-gray-500">Créée le</p>
                <p className="font-medium">{formatDate(observation.createdAt)}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Modifiée le</p>
                <p className="font-medium">{formatDate(observation.updatedAt)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Building className="h-4 w-4" />
              Audit
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-gray-500">Titre</p>
                <p className="font-medium">{observation.audit.title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Organisation</p>
                <p className="font-medium">{observation.audit.organization.name}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-gray-500">Actions liées</p>
                <p className="font-medium text-2xl">{observation._count.actions}</p>
              </div>
              <div>
                <p className="text-xs text-gray-500">
                  Actions correctives à mettre en place
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Onglets de détail */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
          <TabsTrigger value="actions">Actions correctives</TabsTrigger>
          <TabsTrigger value="evidence">Preuves</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Description</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 whitespace-pre-wrap">{observation.description}</p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Actions correctives</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune action</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Les actions correctives seront affichées ici une fois créées.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="evidence" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Preuves et documents</CardTitle>
            </CardHeader>
            <CardContent>
              {observation.evidence ? (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm font-medium text-gray-700 mb-2">Preuves</p>
                  <p className="text-gray-600 whitespace-pre-wrap">{observation.evidence}</p>
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune preuve</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Aucune preuve n'a été fournie pour cette observation.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
