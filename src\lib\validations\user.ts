import * as z from "zod"

// User roles enum
export const USER_ROLES = {
  SUPER_ADMIN: "SUPER_ADMIN",
  ADMIN: "ADMIN",
  MANAGER: "MANAGER",
  AUDITOR: "AUDITOR",
  USER: "USER"
} as const

export type UserRole = keyof typeof USER_ROLES

// Role enum for validation
const roleEnum = z.enum(["SUPER_ADMIN", "ADMIN", "MANAGER", "AUDITOR", "USER"])

// User creation schema
export const createUserSchema = z.object({
  name: z.string()
    .min(2, "Le nom doit contenir au moins 2 caractères")
    .max(100, "Le nom ne peut pas dépasser 100 caractères")
    .trim(),
  email: z.string()
    .email("Adresse email invalide")
    .toLowerCase()
    .trim(),
  password: z.string()
    .min(8, "Le mot de passe doit contenir au moins 8 caractères")
    .max(100, "Le mot de passe ne peut pas dépasser 100 caractères")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre"),
  role: roleEnum.default("USER"),
  organizationId: z.string().cuid("ID d'organisation invalide").optional(),
  isActive: z.boolean().default(true),
})

// User update schema (password optional)
export const updateUserSchema = z.object({
  name: z.string()
    .min(2, "Le nom doit contenir au moins 2 caractères")
    .max(100, "Le nom ne peut pas dépasser 100 caractères")
    .trim()
    .optional(),
  email: z.string()
    .email("Adresse email invalide")
    .toLowerCase()
    .trim()
    .optional(),
  password: z.string()
    .min(8, "Le mot de passe doit contenir au moins 8 caractères")
    .max(100, "Le mot de passe ne peut pas dépasser 100 caractères")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre")
    .optional(),
  role: roleEnum.optional(),
  organizationId: z.string().cuid("ID d'organisation invalide").optional(),
  isActive: z.boolean().optional(),
})

// User filter schema
export const userFilterSchema = z.object({
  search: z.string().trim().optional(),
  role: roleEnum.optional(),
  organizationId: z.string().cuid("ID d'organisation invalide").optional(),
  isActive: z.boolean().optional(),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  sortBy: z.enum(["name", "email", "role", "createdAt", "lastLoginAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
})

// Password change schema
export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Mot de passe actuel requis"),
  newPassword: z.string()
    .min(8, "Le nouveau mot de passe doit contenir au moins 8 caractères")
    .max(100, "Le mot de passe ne peut pas dépasser 100 caractères")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Les mots de passe ne correspondent pas",
  path: ["confirmPassword"],
})

// User response schema (for API responses)
export const userResponseSchema = z.object({
  id: z.string(),
  name: z.string().nullable(),
  email: z.string(),
  role: roleEnum,
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
  lastLoginAt: z.date().nullable(),
  organizationId: z.string().nullable(),
  organization: z.object({
    id: z.string(),
    name: z.string(),
  }).nullable().optional(),
})

// Bulk user operations schema
export const bulkUserOperationSchema = z.object({
  userIds: z.array(z.string().cuid()).min(1, "Au moins un utilisateur doit être sélectionné"),
  operation: z.enum(["activate", "deactivate", "delete"]),
})

export type CreateUserData = z.infer<typeof createUserSchema>
export type UpdateUserData = z.infer<typeof updateUserSchema>
export type UserFilterData = z.infer<typeof userFilterSchema>
export type ChangePasswordData = z.infer<typeof changePasswordSchema>
export type UserResponseData = z.infer<typeof userResponseSchema>
export type BulkUserOperationData = z.infer<typeof bulkUserOperationSchema>

// Role labels for display
export const ROLE_LABELS: Record<UserRole, string> = {
  SUPER_ADMIN: "Super Administrateur",
  ADMIN: "Administrateur",
  MANAGER: "Gestionnaire",
  AUDITOR: "Auditeur",
  USER: "Utilisateur"
}

// Role colors for badges (using Magneto colors)
export const ROLE_COLORS: Record<UserRole, string> = {
  SUPER_ADMIN: "bg-red-100 text-red-800 border-red-200",
  ADMIN: "bg-purple-100 text-purple-800 border-purple-200",
  MANAGER: "bg-blue-100 text-blue-800 border-blue-200",
  AUDITOR: "bg-green-100 text-green-800 border-green-200",
  USER: "bg-gray-100 text-gray-800 border-gray-200"
}

// Role permissions
export const ROLE_PERMISSIONS = {
  SUPER_ADMIN: ["*"], // All permissions
  ADMIN: ["users:read", "users:create", "users:update", "users:delete", "audits:*", "observations:*", "actions:*", "reports:*"],
  MANAGER: ["users:read", "audits:read", "audits:create", "audits:update", "observations:*", "actions:*", "reports:read", "reports:create"],
  AUDITOR: ["audits:read", "audits:update", "observations:read", "observations:create", "observations:update", "actions:read", "actions:create", "actions:update", "reports:read"],
  USER: ["audits:read", "observations:read", "actions:read", "reports:read"]
} as const

// Helper function to check permissions
export function hasPermission(userRole: UserRole, resource: string, action: string): boolean {
  const permissions = ROLE_PERMISSIONS[userRole]

  // Super admin has all permissions
  if (permissions.includes("*")) {
    return true
  }

  const fullPermission = `${resource}:${action}`
  const resourceWildcard = `${resource}:*`

  return permissions.includes(fullPermission) || permissions.includes(resourceWildcard)
}
