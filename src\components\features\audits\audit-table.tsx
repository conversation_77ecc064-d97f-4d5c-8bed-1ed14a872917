"use client"

import { useState } from "react"
import { useAuditPermissions } from "@/components/auth/audit-permission-guard"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { AuditStatusBadge } from "./audit-status-badge"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Eye, Edit, Trash2, Users, Calendar, MoreHorizontal } from "lucide-react"
import { format } from "date-fns"
import { fr } from "date-fns/locale"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface AuditTableProps {
  audits: AuditWithRelations[]
  onView?: (audit: AuditWithRelations) => void
  onEdit?: (audit: AuditWithRelations) => void
  onDelete?: (audit: AuditWithRelations) => void
  onSelectionChange?: (selectedIds: string[]) => void
  loading?: boolean
}

export function AuditTable({
  audits,
  onView,
  onEdit,
  onDelete,
  onSelectionChange,
  loading = false
}: AuditTableProps) {
  const [selectedIds, setSelectedIds] = useState<string[]>([])
  const { canUpdate, canDelete } = useAuditPermissions()

  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: fr })
  }

  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? audits.map(audit => audit.id) : []
    setSelectedIds(newSelection)
    onSelectionChange?.(newSelection)
  }

  const handleSelectOne = (auditId: string, checked: boolean) => {
    const newSelection = checked 
      ? [...selectedIds, auditId]
      : selectedIds.filter(id => id !== auditId)
    
    setSelectedIds(newSelection)
    onSelectionChange?.(newSelection)
  }

  const isAllSelected = audits.length > 0 && selectedIds.length === audits.length
  const isPartiallySelected = selectedIds.length > 0 && selectedIds.length < audits.length

  if (loading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  if (audits.length === 0) {
    return (
      <div className="text-center py-12">
        <Calendar className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun audit</h3>
        <p className="mt-1 text-sm text-gray-500">
          Commencez par créer votre premier audit.
        </p>
      </div>
    )
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={isAllSelected}
                indeterminate={isPartiallySelected}
                onCheckedChange={handleSelectAll}
              />
            </TableHead>
            <TableHead>Titre</TableHead>
            <TableHead>Organisation</TableHead>
            <TableHead>Statut</TableHead>
            <TableHead>Date de début</TableHead>
            <TableHead>Auditeurs</TableHead>
            <TableHead>Observations</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {audits.map((audit) => {
            const leadAuditor = audit.auditors.find(a => a.role === "LEAD_AUDITOR")
            const isSelected = selectedIds.includes(audit.id)

            return (
              <TableRow 
                key={audit.id}
                className={isSelected ? "bg-blue-50" : ""}
              >
                <TableCell>
                  <Checkbox
                    checked={isSelected}
                    onCheckedChange={(checked) => handleSelectOne(audit.id, checked as boolean)}
                  />
                </TableCell>
                
                <TableCell>
                  <div>
                    <div className="font-medium text-gray-900">{audit.title}</div>
                    {audit.description && (
                      <div className="text-sm text-gray-500 truncate max-w-xs">
                        {audit.description}
                      </div>
                    )}
                  </div>
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-gray-900">
                    {audit.organization.name}
                  </div>
                </TableCell>
                
                <TableCell>
                  <AuditStatusBadge status={audit.status} />
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-gray-900">
                    {formatDate(audit.startDate)}
                  </div>
                  {audit.endDate && (
                    <div className="text-xs text-gray-500">
                      Fin: {formatDate(audit.endDate)}
                    </div>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3 text-gray-400" />
                    <span className="text-sm text-gray-900">
                      {audit.auditors.length}
                    </span>
                  </div>
                  {leadAuditor && (
                    <div className="text-xs text-gray-500 truncate max-w-24">
                      {leadAuditor.user.name || leadAuditor.user.email}
                    </div>
                  )}
                </TableCell>
                
                <TableCell>
                  <div className="text-sm text-gray-900">
                    {audit._count.observations}
                  </div>
                  <div className="text-xs text-gray-500">
                    {audit._count.actions} actions
                  </div>
                </TableCell>
                
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {onView && (
                        <DropdownMenuItem onClick={() => onView(audit)}>
                          <Eye className="mr-2 h-4 w-4" />
                          Voir les détails
                        </DropdownMenuItem>
                      )}

                      {onEdit && canUpdate(audit) && (
                        <DropdownMenuItem onClick={() => onEdit(audit)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Modifier
                        </DropdownMenuItem>
                      )}

                      {onDelete && canDelete(audit) && audit.status === 'PLANNED' && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => onDelete(audit)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Supprimer
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
