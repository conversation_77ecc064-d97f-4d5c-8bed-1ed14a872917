"use client"

import { ReactNode } from "react"
import { useSession } from "@/lib/auth/client"
import { hasPermission, UserRole } from "@/lib/validations/user"
import { AuditWithRelations } from "@/lib/validations/audit"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Lock } from "lucide-react"

interface AuditPermissionGuardProps {
  children: ReactNode
  resource: string
  action: string
  audit?: AuditWithRelations
  fallback?: ReactNode
  showError?: boolean
}

export function AuditPermissionGuard({
  children,
  resource,
  action,
  audit,
  fallback,
  showError = true
}: AuditPermissionGuardProps) {
  const { data: session, isPending } = useSession()

  if (isPending) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded" />
  }

  if (!session?.user) {
    if (showError) {
      return (
        <Alert variant="destructive">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Vous devez être connecté pour accéder à cette fonctionnalité.
          </AlertDescription>
        </Alert>
      )
    }
    return fallback || null
  }

  const user = session.user
  const userRole = user.role as UserRole

  // Vérification des permissions de base
  if (!hasPermission(userRole, resource, action)) {
    if (showError) {
      return (
        <Alert variant="destructive">
          <Lock className="h-4 w-4" />
          <AlertDescription>
            Vous n'avez pas les permissions nécessaires pour cette action.
          </AlertDescription>
        </Alert>
      )
    }
    return fallback || null
  }

  // Vérifications spécifiques aux audits
  if (audit) {
    const canAccess = checkAuditAccess(user, audit, action)
    
    if (!canAccess) {
      if (showError) {
        return (
          <Alert variant="destructive">
            <Lock className="h-4 w-4" />
            <AlertDescription>
              Vous n'avez pas accès à cet audit.
            </AlertDescription>
          </Alert>
        )
      }
      return fallback || null
    }
  }

  return <>{children}</>
}

function checkAuditAccess(user: any, audit: AuditWithRelations, action: string): boolean {
  const userRole = user.role as UserRole

  // Super admin a accès à tout
  if (userRole === UserRole.SUPER_ADMIN) {
    return true
  }

  // Vérifier l'organisation
  if (userRole !== UserRole.SUPER_ADMIN && user.organizationId !== audit.organizationId) {
    return false
  }

  // Vérifications spécifiques par action
  switch (action) {
    case "read":
      // Lecture : tous les membres de l'organisation + auditeurs assignés
      return (
        user.organizationId === audit.organizationId ||
        audit.auditors.some(a => a.user.id === user.id) ||
        audit.creatorId === user.id
      )

    case "update":
      // Modification : managers, créateur, auditeur principal
      return (
        userRole === UserRole.MANAGER ||
        audit.creatorId === user.id ||
        audit.auditors.some(a => a.user.id === user.id && a.role === "LEAD_AUDITOR")
      )

    case "delete":
      // Suppression : managers, créateur (seulement si planifié)
      return (
        audit.status === "PLANNED" && (
          userRole === UserRole.MANAGER ||
          audit.creatorId === user.id
        )
      )

    case "assign_auditors":
      // Assignation d'auditeurs : managers, créateur, auditeur principal
      return (
        userRole === UserRole.MANAGER ||
        audit.creatorId === user.id ||
        audit.auditors.some(a => a.user.id === user.id && a.role === "LEAD_AUDITOR")
      )

    case "create_observation":
      // Création d'observation : auditeurs assignés
      return audit.auditors.some(a => a.user.id === user.id)

    case "create_action":
      // Création d'action : auditeurs assignés
      return audit.auditors.some(a => a.user.id === user.id)

    case "generate_report":
      // Génération de rapport : auditeur principal, managers
      return (
        userRole === UserRole.MANAGER ||
        audit.auditors.some(a => a.user.id === user.id && a.role === "LEAD_AUDITOR")
      )

    default:
      return false
  }
}

// Hook pour vérifier les permissions d'audit
export function useAuditPermissions(audit?: AuditWithRelations) {
  const { data: session } = useSession()

  const checkPermission = (resource: string, action: string): boolean => {
    if (!session?.user) return false

    const user = session.user
    const userRole = user.role as UserRole

    // Vérification des permissions de base
    if (!hasPermission(userRole, resource, action)) {
      return false
    }

    // Vérifications spécifiques aux audits
    if (audit) {
      return checkAuditAccess(user, audit, action)
    }

    return true
  }

  return {
    canRead: (audit?: AuditWithRelations) => checkPermission("audits", "read"),
    canCreate: () => checkPermission("audits", "create"),
    canUpdate: (audit?: AuditWithRelations) => checkPermission("audits", "update"),
    canDelete: (audit?: AuditWithRelations) => checkPermission("audits", "delete"),
    canAssignAuditors: (audit?: AuditWithRelations) => checkPermission("audits", "assign_auditors"),
    canCreateObservation: (audit?: AuditWithRelations) => checkPermission("audits", "create_observation"),
    canCreateAction: (audit?: AuditWithRelations) => checkPermission("audits", "create_action"),
    canGenerateReport: (audit?: AuditWithRelations) => checkPermission("audits", "generate_report"),
    user: session?.user
  }
}
