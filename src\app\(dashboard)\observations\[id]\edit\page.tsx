"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"

interface EditObservationPageProps {
  params: {
    id: string
  }
}

export default function EditObservationPage({ params }: EditObservationPageProps) {
  const router = useRouter()

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>
        
        <div>
          <h1 className="text-3xl font-bold magneto-title">Modifier l'observation</h1>
          <p className="text-gray-600">
            Modifiez les informations de l'observation
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de l'observation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <p className="text-gray-500">
              Le formulaire d'édition d'observation sera implémenté dans la prochaine étape.
            </p>
            <p className="text-sm text-gray-400 mt-2">
              ID de l'observation: {params.id}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
