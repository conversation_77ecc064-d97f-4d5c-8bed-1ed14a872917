"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { useRouter } from "next/navigation"

export default function NewAuditPage() {
  const router = useRouter()

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Retour
        </Button>
        
        <div>
          <h1 className="text-3xl font-bold magneto-title">Nouvel audit</h1>
          <p className="text-gray-600">
            Créez un nouvel audit et assignez les auditeurs
          </p>
        </div>
      </div>

      {/* Formulaire */}
      <Card>
        <CardHeader>
          <CardTitle>Informations de l'audit</CardTitle>
        </CardHeader>
        <Card<PERSON>ontent>
          <div className="text-center py-12">
            <p className="text-gray-500">
              Le formulaire de création d'audit sera implémenté dans la prochaine étape.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
